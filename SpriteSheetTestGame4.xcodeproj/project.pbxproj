// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		7468D8732DFDFAB400EB6704 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7468D8512DFDFAB200EB6704 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7468D8582DFDFAB200EB6704;
			remoteInfo = SpriteSheetTestGame4;
		};
		7468D87D2DFDFAB400EB6704 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7468D8512DFDFAB200EB6704 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7468D8582DFDFAB200EB6704;
			remoteInfo = SpriteSheetTestGame4;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7468D8592DFDFAB200EB6704 /* SpriteSheetTestGame4.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SpriteSheetTestGame4.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7468D8722DFDFAB400EB6704 /* SpriteSheetTestGame4Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SpriteSheetTestGame4Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7468D87C2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SpriteSheetTestGame4UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7468D85B2DFDFAB200EB6704 /* SpriteSheetTestGame4 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SpriteSheetTestGame4;
			sourceTree = "<group>";
		};
		7468D8752DFDFAB400EB6704 /* SpriteSheetTestGame4Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SpriteSheetTestGame4Tests;
			sourceTree = "<group>";
		};
		7468D87F2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SpriteSheetTestGame4UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7468D8562DFDFAB200EB6704 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D86F2DFDFAB400EB6704 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D8792DFDFAB400EB6704 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7468D8502DFDFAB200EB6704 = {
			isa = PBXGroup;
			children = (
				7468D85B2DFDFAB200EB6704 /* SpriteSheetTestGame4 */,
				7468D8752DFDFAB400EB6704 /* SpriteSheetTestGame4Tests */,
				7468D87F2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests */,
				7468D85A2DFDFAB200EB6704 /* Products */,
			);
			sourceTree = "<group>";
		};
		7468D85A2DFDFAB200EB6704 /* Products */ = {
			isa = PBXGroup;
			children = (
				7468D8592DFDFAB200EB6704 /* SpriteSheetTestGame4.app */,
				7468D8722DFDFAB400EB6704 /* SpriteSheetTestGame4Tests.xctest */,
				7468D87C2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7468D8582DFDFAB200EB6704 /* SpriteSheetTestGame4 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7468D8862DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4" */;
			buildPhases = (
				7468D8552DFDFAB200EB6704 /* Sources */,
				7468D8562DFDFAB200EB6704 /* Frameworks */,
				7468D8572DFDFAB200EB6704 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7468D85B2DFDFAB200EB6704 /* SpriteSheetTestGame4 */,
			);
			name = SpriteSheetTestGame4;
			packageProductDependencies = (
			);
			productName = SpriteSheetTestGame4;
			productReference = 7468D8592DFDFAB200EB6704 /* SpriteSheetTestGame4.app */;
			productType = "com.apple.product-type.application";
		};
		7468D8712DFDFAB400EB6704 /* SpriteSheetTestGame4Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7468D8892DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4Tests" */;
			buildPhases = (
				7468D86E2DFDFAB400EB6704 /* Sources */,
				7468D86F2DFDFAB400EB6704 /* Frameworks */,
				7468D8702DFDFAB400EB6704 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7468D8742DFDFAB400EB6704 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7468D8752DFDFAB400EB6704 /* SpriteSheetTestGame4Tests */,
			);
			name = SpriteSheetTestGame4Tests;
			packageProductDependencies = (
			);
			productName = SpriteSheetTestGame4Tests;
			productReference = 7468D8722DFDFAB400EB6704 /* SpriteSheetTestGame4Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7468D87B2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7468D88C2DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4UITests" */;
			buildPhases = (
				7468D8782DFDFAB400EB6704 /* Sources */,
				7468D8792DFDFAB400EB6704 /* Frameworks */,
				7468D87A2DFDFAB400EB6704 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7468D87E2DFDFAB400EB6704 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7468D87F2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests */,
			);
			name = SpriteSheetTestGame4UITests;
			packageProductDependencies = (
			);
			productName = SpriteSheetTestGame4UITests;
			productReference = 7468D87C2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7468D8512DFDFAB200EB6704 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					7468D8582DFDFAB200EB6704 = {
						CreatedOnToolsVersion = 16.4;
					};
					7468D8712DFDFAB400EB6704 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7468D8582DFDFAB200EB6704;
					};
					7468D87B2DFDFAB400EB6704 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7468D8582DFDFAB200EB6704;
					};
				};
			};
			buildConfigurationList = 7468D8542DFDFAB200EB6704 /* Build configuration list for PBXProject "SpriteSheetTestGame4" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7468D8502DFDFAB200EB6704;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7468D85A2DFDFAB200EB6704 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7468D8582DFDFAB200EB6704 /* SpriteSheetTestGame4 */,
				7468D8712DFDFAB400EB6704 /* SpriteSheetTestGame4Tests */,
				7468D87B2DFDFAB400EB6704 /* SpriteSheetTestGame4UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7468D8572DFDFAB200EB6704 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D8702DFDFAB400EB6704 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D87A2DFDFAB400EB6704 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7468D8552DFDFAB200EB6704 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D86E2DFDFAB400EB6704 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7468D8782DFDFAB400EB6704 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7468D8742DFDFAB400EB6704 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7468D8582DFDFAB200EB6704 /* SpriteSheetTestGame4 */;
			targetProxy = 7468D8732DFDFAB400EB6704 /* PBXContainerItemProxy */;
		};
		7468D87E2DFDFAB400EB6704 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7468D8582DFDFAB200EB6704 /* SpriteSheetTestGame4 */;
			targetProxy = 7468D87D2DFDFAB400EB6704 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7468D8842DFDFAB400EB6704 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7468D8852DFDFAB400EB6704 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7468D8872DFDFAB400EB6704 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7468D8882DFDFAB400EB6704 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7468D88A2DFDFAB400EB6704 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SpriteSheetTestGame4.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SpriteSheetTestGame4";
			};
			name = Debug;
		};
		7468D88B2DFDFAB400EB6704 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SpriteSheetTestGame4.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SpriteSheetTestGame4";
			};
			name = Release;
		};
		7468D88D2DFDFAB400EB6704 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SpriteSheetTestGame4;
			};
			name = Debug;
		};
		7468D88E2DFDFAB400EB6704 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8WTH92NXLW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alliconsulting.app.SpriteSheetTestGame4UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SpriteSheetTestGame4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7468D8542DFDFAB200EB6704 /* Build configuration list for PBXProject "SpriteSheetTestGame4" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7468D8842DFDFAB400EB6704 /* Debug */,
				7468D8852DFDFAB400EB6704 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7468D8862DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7468D8872DFDFAB400EB6704 /* Debug */,
				7468D8882DFDFAB400EB6704 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7468D8892DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7468D88A2DFDFAB400EB6704 /* Debug */,
				7468D88B2DFDFAB400EB6704 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7468D88C2DFDFAB400EB6704 /* Build configuration list for PBXNativeTarget "SpriteSheetTestGame4UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7468D88D2DFDFAB400EB6704 /* Debug */,
				7468D88E2DFDFAB400EB6704 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7468D8512DFDFAB200EB6704 /* Project object */;
}
