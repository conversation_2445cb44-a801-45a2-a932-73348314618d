//
//  SpriteSheetAnimatorTests.swift
//  SpriteSheetTestGame4Tests
//
//  Created by <PERSON><PERSON> on 2025-06-14.
//

import Testing
import SpriteKit
@testable import SpriteSheetTestGame4

struct SpriteSheetAnimatorTests {

    @Test func testSpriteSheetAnimatorInitialization() async throws {
        // Test that the animator initializes correctly with valid parameters
        let animator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 64, height: 64),
            framesPerRow: 4,
            totalFrames: 4
        )
        
        #expect(animator != nil, "Animator should initialize successfully")
        #expect(animator?.getFrameCount() == 4, "Should have 4 frames")
        #expect(animator?.getFirstTexture() != nil, "Should have a first texture")
    }
    
    @Test func testSpriteSheetAnimatorWithInvalidImage() async throws {
        // Test that the animator fails gracefully with invalid image name
        let animator = SpriteSheetAnimator(
            spriteSheetName: "NonExistentImage",
            frameSize: CGSize(width: 64, height: 64),
            framesPerRow: 4,
            totalFrames: 4
        )
        
        #expect(animator == nil, "Animator should fail to initialize with invalid image name")
    }
    
    @Test func testAnimationCreation() async throws {
        let animator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 64, height: 64),
            framesPerRow: 4,
            totalFrames: 4
        )
        
        guard let animator = animator else {
            Issue.record("Failed to create animator")
            return
        }
        
        let animation = animator.createAnimation(duration: 1.0, repeatForever: true)
        #expect(animation.duration > 0, "Animation should have positive duration")
    }
    
    @Test func testTextureAccess() async throws {
        let animator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 64, height: 64),
            framesPerRow: 4,
            totalFrames: 4
        )
        
        guard let animator = animator else {
            Issue.record("Failed to create animator")
            return
        }
        
        // Test valid texture access
        let firstTexture = animator.getTexture(at: 0)
        #expect(firstTexture != nil, "Should be able to get first texture")
        
        // Test invalid texture access
        let invalidTexture = animator.getTexture(at: 10)
        #expect(invalidTexture == nil, "Should return nil for invalid index")
    }
    
    @Test func testCustomAnimation() async throws {
        let animator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 64, height: 64),
            framesPerRow: 4,
            totalFrames: 4
        )
        
        guard let animator = animator else {
            Issue.record("Failed to create animator")
            return
        }
        
        // Test custom animation with specific frame indices
        let customAnimation = animator.createCustomAnimation(
            frameIndices: [0, 1, 2, 1],
            duration: 1.0,
            repeatForever: false
        )
        
        #expect(customAnimation.duration > 0, "Custom animation should have positive duration")
    }
}
