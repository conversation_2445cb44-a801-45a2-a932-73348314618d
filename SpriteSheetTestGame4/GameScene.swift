//
//  GameScene.swift
//  SpriteSheetTestGame4
//
//  Created by <PERSON><PERSON> on 2025-06-14.
//

import SpriteKit
import GameplayKit

class GameScene: SKScene {

    private var label : SKLabelNode?
    private var spinnyNode : SKShapeNode?
    private var boySprite: SKSpriteNode?
    private var spriteSheetAnimator: SpriteSheetAnimator?
    
    override func didMove(to view: SKView) {

        // Get label node from scene and store it for use later
        self.label = self.childNode(withName: "//helloLabel") as? SKLabelNode
        if let label = self.label {
            label.alpha = 0.0
            label.run(SKAction.fadeIn(withDuration: 2.0))
        }

        // Create shape node to use during mouse interaction
        let w = (self.size.width + self.size.height) * 0.05
        self.spinnyNode = SKShapeNode.init(rectOf: CGSize.init(width: w, height: w), cornerRadius: w * 0.3)

        if let spinnyNode = self.spinnyNode {
            spinnyNode.lineWidth = 2.5

            spinnyNode.run(SKAction.repeatForever(SKAction.rotate(byAngle: CGFloat(Double.pi), duration: 1)))
            spinnyNode.run(SKAction.sequence([SKAction.wait(forDuration: 0.5),
                                              SKAction.fadeOut(withDuration: 0.5),
                                              SKAction.removeFromParent()]))
        }

        // Setup boy character sprite sheet animation
        setupBoyCharacter()
    }
    
    
    func touchDown(atPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.green
            self.addChild(n)
        }
    }
    
    func touchMoved(toPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.blue
            self.addChild(n)
        }
    }
    
    func touchUp(atPoint pos : CGPoint) {
        if let n = self.spinnyNode?.copy() as! SKShapeNode? {
            n.position = pos
            n.strokeColor = SKColor.red
            self.addChild(n)
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let label = self.label {
            label.run(SKAction.init(named: "Pulse")!, withKey: "fadeInOut")
        }

        // Toggle boy animation on touch
        toggleBoyAnimation()

        for t in touches { self.touchDown(atPoint: t.location(in: self)) }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchMoved(toPoint: t.location(in: self)) }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchUp(atPoint: t.location(in: self)) }
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        for t in touches { self.touchUp(atPoint: t.location(in: self)) }
    }
    
    
    override func update(_ currentTime: TimeInterval) {
        // Called before each frame is rendered
    }

    // MARK: - Boy Character Setup and Animation
    private func setupBoyCharacter() {
        // Initialize the sprite sheet animator
        // Assuming the sprite sheet has 4 frames in a single row
        // You may need to adjust these values based on your actual sprite sheet dimensions
        spriteSheetAnimator = SpriteSheetAnimator(
            spriteSheetName: "BoyWalkRight_Sheet",
            frameSize: CGSize(width: 64, height: 64), // Adjust based on your sprite sheet
            framesPerRow: 4, // Adjust based on your sprite sheet layout
            totalFrames: 4   // Adjust based on total number of frames
        )

        guard let animator = spriteSheetAnimator,
              let firstTexture = animator.getFirstTexture() else {
            print("Error: Could not initialize sprite sheet animator")
            return
        }

        // Create the boy sprite with the first frame
        boySprite = SKSpriteNode(texture: firstTexture)
        boySprite?.position = CGPoint(x: frame.midX, y: frame.midY - 100)
        boySprite?.setScale(2.0) // Make it bigger for visibility

        if let boySprite = boySprite {
            addChild(boySprite)
        }

        // Start the walking animation
        startBoyWalkingAnimation()
    }

    private func startBoyWalkingAnimation() {
        guard let boySprite = boySprite,
              let animator = spriteSheetAnimator else { return }

        let walkingAnimation = animator.createAnimation(duration: 0.8, repeatForever: true)
        boySprite.run(walkingAnimation, withKey: "walking")
    }

    private func stopBoyWalkingAnimation() {
        boySprite?.removeAction(forKey: "walking")
    }

    private func toggleBoyAnimation() {
        guard let boySprite = boySprite else { return }

        if boySprite.action(forKey: "walking") != nil {
            stopBoyWalkingAnimation()
            print("Boy animation stopped")
        } else {
            startBoyWalkingAnimation()
            print("Boy animation started")
        }
    }
}
