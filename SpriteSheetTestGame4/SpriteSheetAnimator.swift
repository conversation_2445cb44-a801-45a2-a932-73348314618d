//
//  SpriteSheetAnimator.swift
//  SpriteSheetTestGame4
//
//  Created by <PERSON><PERSON> on 2025-06-14.
//

import SpriteKit
import UIKit

class SpriteSheetAnimator {
    
    // MARK: - Properties
    private let spriteSheet: UIImage
    private let frameSize: CGSize
    private let framesPerRow: Int
    private let totalFrames: Int
    private var textures: [SKTexture] = []
    
    // MARK: - Initialization
    init?(spriteSheetName: String, frameSize: CGSize, framesPerRow: Int, totalFrames: Int) {
        guard let image = UIImage(named: spriteSheetName) else {
            print("Error: Could not load sprite sheet image named '\(spriteSheetName)'")
            return nil
        }
        
        self.spriteSheet = image
        self.frameSize = frameSize
        self.framesPerRow = framesPerRow
        self.totalFrames = totalFrames
        
        extractFrames()
    }
    
    // MARK: - Frame Extraction
    private func extractFrames() {
        guard let cgImage = spriteSheet.cgImage else {
            print("Error: Could not get CGImage from sprite sheet")
            return
        }
        
        textures.removeAll()
        
        for frameIndex in 0..<totalFrames {
            let row = frameIndex / framesPerRow
            let col = frameIndex % framesPerRow
            
            let x = CGFloat(col) * frameSize.width
            let y = CGFloat(row) * frameSize.height
            
            let frameRect = CGRect(x: x, y: y, width: frameSize.width, height: frameSize.height)
            
            if let frameImage = cgImage.cropping(to: frameRect) {
                let texture = SKTexture(cgImage: frameImage)
                texture.filteringMode = .nearest // Prevents blurring for pixel art
                textures.append(texture)
            }
        }
        
        print("Extracted \(textures.count) frames from sprite sheet")
    }
    
    // MARK: - Animation Creation
    func createAnimation(duration: TimeInterval, repeatForever: Bool = true) -> SKAction {
        guard !textures.isEmpty else {
            print("Error: No textures available for animation")
            return SKAction()
        }
        
        let animateAction = SKAction.animate(with: textures, timePerFrame: duration / Double(textures.count))
        
        if repeatForever {
            return SKAction.repeatForever(animateAction)
        } else {
            return animateAction
        }
    }
    
    // MARK: - Texture Access
    func getTexture(at index: Int) -> SKTexture? {
        guard index >= 0 && index < textures.count else {
            print("Error: Frame index \(index) out of bounds")
            return nil
        }
        return textures[index]
    }
    
    func getFirstTexture() -> SKTexture? {
        return textures.first
    }
    
    func getFrameCount() -> Int {
        return textures.count
    }
    
    // MARK: - Custom Animation Creation
    func createCustomAnimation(frameIndices: [Int], duration: TimeInterval, repeatForever: Bool = true) -> SKAction {
        let customTextures = frameIndices.compactMap { getTexture(at: $0) }
        
        guard !customTextures.isEmpty else {
            print("Error: No valid textures for custom animation")
            return SKAction()
        }
        
        let animateAction = SKAction.animate(with: customTextures, timePerFrame: duration / Double(customTextures.count))
        
        if repeatForever {
            return SKAction.repeatForever(animateAction)
        } else {
            return animateAction
        }
    }
}
