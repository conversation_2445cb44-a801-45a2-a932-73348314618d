# SpriteSheet Animation System

This project demonstrates how to implement sprite sheet animation in SpriteKit using a custom `SpriteSheetAnimator` class.

## Overview

The sprite sheet animation system consists of two main components:

1. **SpriteSheetAnimator** - A reusable utility class for extracting frames from sprite sheets and creating animations
2. **GameScene** - The main game scene that uses the animator to display animated sprites

## Features

- ✅ Automatic frame extraction from sprite sheets
- ✅ Configurable frame size and layout
- ✅ Support for repeating and non-repeating animations
- ✅ Custom animation sequences with specific frame indices
- ✅ Pixel-perfect rendering with nearest neighbor filtering
- ✅ Touch interaction to start/stop animations
- ✅ Comprehensive error handling and logging

## How to Use

### 1. Basic Setup

```swift
// Initialize the sprite sheet animator
let animator = SpriteSheetAnimator(
    spriteSheetName: "BoyWalkRight_Sheet",  // Name of your sprite sheet image
    frameSize: CGSize(width: 64, height: 64), // Size of each frame
    framesPerRow: 4,                        // Number of frames per row
    totalFrames: 4                          // Total number of frames
)
```

### 2. Create a Sprite with Animation

```swift
// Get the first texture for the initial sprite
guard let firstTexture = animator?.getFirstTexture() else { return }

// Create the sprite
let sprite = SKSpriteNode(texture: firstTexture)
sprite.position = CGPoint(x: 100, y: 100)
addChild(sprite)

// Create and run the animation
let walkingAnimation = animator.createAnimation(duration: 0.8, repeatForever: true)
sprite.run(walkingAnimation, withKey: "walking")
```

### 3. Custom Animation Sequences

```swift
// Create a custom animation using specific frame indices
let customAnimation = animator.createCustomAnimation(
    frameIndices: [0, 1, 2, 1], // Custom sequence: frame 0 -> 1 -> 2 -> 1
    duration: 1.0,
    repeatForever: false
)
sprite.run(customAnimation)
```

### 4. Animation Control

```swift
// Stop animation
sprite.removeAction(forKey: "walking")

// Check if animation is running
if sprite.action(forKey: "walking") != nil {
    print("Animation is running")
}
```

## Sprite Sheet Requirements

Your sprite sheet should be organized as a grid where:
- All frames are the same size
- Frames are arranged left-to-right, top-to-bottom
- No spacing between frames
- Power-of-2 dimensions recommended for optimal performance

Example sprite sheet layout for a 4-frame walking animation:
```
[Frame 0][Frame 1][Frame 2][Frame 3]
```

## Configuration Parameters

### SpriteSheetAnimator Parameters

- **spriteSheetName**: Name of the image file in your asset catalog
- **frameSize**: CGSize representing the width and height of each frame
- **framesPerRow**: Number of frames in each horizontal row
- **totalFrames**: Total number of animation frames to extract

### Animation Parameters

- **duration**: Total time for one complete animation cycle
- **repeatForever**: Whether the animation should loop continuously
- **frameIndices**: (Custom animations only) Array of frame indices to use

## Current Implementation

The current implementation in `GameScene.swift`:

1. **Initialization**: Sets up the boy character sprite with the walking animation
2. **Touch Interaction**: Tap anywhere on the screen to toggle the animation on/off
3. **Default Settings**: 
   - Frame size: 64x64 pixels
   - 4 frames per row
   - 4 total frames
   - Animation duration: 0.8 seconds
   - 2x scale for better visibility

## Customization

To use your own sprite sheet:

1. Add your sprite sheet image to the Assets.xcassets catalog
2. Update the parameters in `setupBoyCharacter()`:
   ```swift
   spriteSheetAnimator = SpriteSheetAnimator(
       spriteSheetName: "YourSpriteSheetName",
       frameSize: CGSize(width: yourFrameWidth, height: yourFrameHeight),
       framesPerRow: yourFramesPerRow,
       totalFrames: yourTotalFrames
   )
   ```

## Testing

The project includes comprehensive unit tests in `SpriteSheetAnimatorTests.swift` that verify:
- Proper initialization with valid parameters
- Graceful failure with invalid parameters
- Animation creation functionality
- Texture access methods
- Custom animation sequences

Run tests using Xcode's Test Navigator or `Cmd+U`.

## Performance Tips

1. Use power-of-2 texture dimensions when possible
2. Keep sprite sheets reasonably sized (avoid very large images)
3. Use `.nearest` filtering mode for pixel art to maintain crisp edges
4. Remove unused animations to free memory
5. Consider using texture atlases for multiple sprite sheets

## Troubleshooting

**Animation not showing:**
- Verify the sprite sheet image is properly added to Assets.xcassets
- Check that frame size matches your actual sprite dimensions
- Ensure framesPerRow and totalFrames are correct

**Blurry sprites:**
- The animator uses `.nearest` filtering mode by default for pixel art
- Avoid fractional positions and scales when possible

**Performance issues:**
- Reduce sprite sheet size or number of frames
- Use fewer simultaneous animations
- Profile using Instruments to identify bottlenecks
